package org.example;

import javafx.application.Application;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.text.Font;
import javafx.stage.Stage;
import javafx.scene.Scene;
import javafx.stage.DirectoryChooser;
import org.apache.pulsar.client.api.PulsarClientException;

import java.io.File;
import java.time.LocalDate;

import static org.example.Main.filePath;
import static org.example.Main.isDateFilterActive;

public class JavaFXApp extends Application {

    private static Label statusLabel; // 将状态标签设为静态变量
    @Override
    public void start(Stage primaryStage) {
        primaryStage.setTitle("G2药水应审软件");

        statusLabel = new Label("当前状态：等待开始"); // 初始化状态标签
        // 创建按钮和设置首选大小
        Button startButton = new Button("开始任务");
        Button pauseButton = new Button("暂停任务");
        Button stopButton = new Button("结束任务");
        Button choosePathButton = new Button("选择路径");
        Button dateFilterButton = new Button("筛选日期");


        // 设置日期选择器的首选大小和格式
        DatePicker startDatePicker = new DatePicker();
        DatePicker endDatePicker = new DatePicker();
        startDatePicker.setPrefSize(120, 30);
        endDatePicker.setPrefSize(120, 30);
        startDatePicker.setValue(LocalDate.now());
        endDatePicker.setValue(LocalDate.now());

        // 设置文本域和标签的首选大小和样式
        TextField pathTextField = new TextField();


        Label pathLabel = new Label("保存路径：");
        Label startDateLabel = new Label("开始日期:");
        Label endDateLabel = new Label("结束日期:");

        // 设置按钮容器的布局
        HBox buttonsBox = new HBox(20, startButton, pauseButton, stopButton, choosePathButton);
        buttonsBox.setAlignment(Pos.CENTER);

        // 设置日期选择器容器的布局
        HBox datePickersBox = new HBox(20, startDateLabel, startDatePicker, endDateLabel, endDatePicker,
                dateFilterButton);
        datePickersBox.setAlignment(Pos.CENTER);

        // 设置路径文本域容器的布局
        HBox pathBox = new HBox(20, pathLabel, pathTextField);
        pathBox.setAlignment(Pos.CENTER);

        // 创建状态显示标签
        statusLabel.setFont(new Font(16)); // 设置字体大小

        // 将所有容器添加到主容器
        VBox mainBox = new VBox(30, buttonsBox, pathBox, datePickersBox, statusLabel);
        mainBox.setAlignment(Pos.CENTER);
        mainBox.setPadding(new Insets(30, 30, 30, 30));

        // 设置场景和显示舞台
        Scene scene = new Scene(mainBox, 700, 400);
        scene.getStylesheets().add(getClass().getResource("/style.css").toExternalForm());
        primaryStage.setScene(scene);
        primaryStage.getIcons().add(new Image(getClass().getResourceAsStream("/icons/app_icon.png")));
        primaryStage.show();

        // 开始任务按钮的事件处理
        startButton.setOnAction(event -> {
//            if (!isPathSelected()) {
//                // 如果路径没有选择，显示警告信息
//                Alert alert = new Alert(Alert.AlertType.WARNING, "请先选择一个保存路径。", ButtonType.OK);
//                alert.showAndWait();
//                statusLabel.setText("当前状态：等待路径选择");
//            } else {
                // 如果路径已选择，正常启动任务
                Main.startTask();
                statusLabel.setText("当前状态：任务运行中");
//            }
        });

        // 暂停任务按钮的事件处理
        pauseButton.setOnAction(event -> {
            Main.pauseTask();
            statusLabel.setText("当前状态：任务暂停");
        });

        // 结束任务按钮的事件处理
        stopButton.setOnAction(event -> {
            try {
                Main.stopTask();
            } catch (PulsarClientException e) {
                throw new RuntimeException(e);
            }
            statusLabel.setText("当前状态：任务已停止");
        });


        // 路径选择按钮的事件处理
        choosePathButton.setOnAction(event -> {
            DirectoryChooser directoryChooser = new DirectoryChooser();
            File selectedDirectory = directoryChooser.showDialog(primaryStage);

            if (selectedDirectory != null) {
                pathTextField.setText(selectedDirectory.getAbsolutePath());
            }
            filePath = selectedDirectory.getAbsolutePath();
        });

        // 日期筛选按钮的事件处理
        dateFilterButton.setOnAction(event -> {
            if (!isPathSelected()) {
                // 如果路径没有选择，显示警告信息
                Alert alert = new Alert(Alert.AlertType.WARNING, "请先选择一个保存路径。", ButtonType.OK);
                alert.showAndWait();
                statusLabel.setText("当前状态：等待路径选择");
            } else {
                isDateFilterActive = true;
                LocalDate startDate = startDatePicker.getValue();
                LocalDate endDate = endDatePicker.getValue();
                Main.setProcessingDates(startDate, endDate);
                System.out.println("开始日期：" + startDate + "，结束日期：" + endDate);
                Main.startTask();
                statusLabel.setText("当前状态：进行日期筛选的任务运行中");
            }

        });

    }

    @Override
    public void stop() {
        // 设置应用程序停止标志
        Main.stopApp();

        // 等待后台线程完成
        if (Main.taskThread != null) {
            try {
                Main.taskThread.join(); // 等待线程完成执行
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 重新设置中断状态
                e.printStackTrace();
            }
        }
    }

    private boolean isPathSelected() {
        return filePath != null && !filePath.trim().isEmpty();
    }

    // 添加一个静态方法来更新状态标签
    public static void updateStatusLabel(String status) {
        Platform.runLater(() -> {
            statusLabel.setText(status);
        });
    }

    // 添加一个方法来显示日期筛选完成的弹窗
    public static void showDateFilterCompletionAlert() {
        Platform.runLater(() -> {
            Alert alert = new Alert(Alert.AlertType.INFORMATION, "日期筛选任务已完成。", ButtonType.OK);
            alert.setHeaderText(null);
            alert.showAndWait();
        });
    }

    public static void main(String[] args) {
        launch(args);
    }
}
