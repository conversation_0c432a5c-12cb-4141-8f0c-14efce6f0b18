package org.example;

import javafx.application.Application;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.control.*;
import javafx.scene.image.Image;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.scene.text.Font;
import javafx.stage.Stage;
import javafx.scene.Scene;
import org.apache.pulsar.client.api.PulsarClientException;

public class JavaFXApp extends Application {

    private static Label statusLabel; // 状态标签
    private static TextArea processInfoArea; // 处理过程信息显示区域

    @Override
    public void start(Stage primaryStage) {
        primaryStage.setTitle("G2药水应审软件");

        statusLabel = new Label("当前状态：等待开始"); // 初始化状态标签

        // 创建按钮
        Button startButton = new Button("开始任务");
        Button pauseButton = new Button("暂停任务");
        Button stopButton = new Button("结束任务");

        // 设置按钮容器的布局
        HBox buttonsBox = new HBox(20, startButton, pauseButton, stopButton);
        buttonsBox.setAlignment(Pos.CENTER);

        // 创建状态显示标签
        statusLabel.setFont(new Font(16)); // 设置字体大小

        // 创建处理过程信息显示区域
        processInfoArea = new TextArea();
        processInfoArea.setEditable(false);
        processInfoArea.setPrefRowCount(15);
        processInfoArea.setPrefColumnCount(80);
        processInfoArea.setWrapText(true);
        processInfoArea.setFont(new Font("Consolas", 12));

        // 添加滚动面板
        ScrollPane scrollPane = new ScrollPane(processInfoArea);
        scrollPane.setFitToWidth(true);
        scrollPane.setPrefHeight(400);

        // 将所有容器添加到主容器
        VBox mainBox = new VBox(20, buttonsBox, statusLabel, scrollPane);
        mainBox.setAlignment(Pos.CENTER);
        mainBox.setPadding(new Insets(20, 20, 20, 20));

        // 设置场景和显示舞台
        Scene scene = new Scene(mainBox, 900, 600);
        scene.getStylesheets().add(getClass().getResource("/style.css").toExternalForm());
        primaryStage.setScene(scene);
        primaryStage.getIcons().add(new Image(getClass().getResourceAsStream("/icons/app_icon.png")));
        primaryStage.show();

        // 开始任务按钮的事件处理
        startButton.setOnAction(event -> {
            Main.startTask();
            statusLabel.setText("当前状态：任务运行中");
            addProcessInfo("任务已启动，开始处理数据...");
        });

        // 暂停任务按钮的事件处理
        pauseButton.setOnAction(event -> {
            Main.pauseTask();
            statusLabel.setText("当前状态：任务暂停");
            addProcessInfo("任务已暂停");
        });

        // 结束任务按钮的事件处理
        stopButton.setOnAction(event -> {
            try {
                Main.stopTask();
            } catch (PulsarClientException e) {
                throw new RuntimeException(e);
            }
            statusLabel.setText("当前状态：任务已停止");
            addProcessInfo("任务已停止");
        });
    }

    @Override
    public void stop() {
        // 设置应用程序停止标志
        Main.stopApp();

        // 等待后台线程完成
        if (Main.taskThread != null) {
            try {
                Main.taskThread.join(); // 等待线程完成执行
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt(); // 重新设置中断状态
                e.printStackTrace();
            }
        }
    }

    // 添加一个静态方法来更新状态标签
    public static void updateStatusLabel(String status) {
        Platform.runLater(() -> {
            statusLabel.setText(status);
        });
    }

    // 添加处理过程信息到文本区域
    public static void addProcessInfo(String info) {
        Platform.runLater(() -> {
            if (processInfoArea != null) {
                String timestamp = java.time.LocalDateTime.now().format(
                    java.time.format.DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
                processInfoArea.appendText("[" + timestamp + "] " + info + "\n");
                // 自动滚动到底部
                processInfoArea.setScrollTop(Double.MAX_VALUE);
            }
        });
    }

    // 清空处理过程信息
    public static void clearProcessInfo() {
        Platform.runLater(() -> {
            if (processInfoArea != null) {
                processInfoArea.clear();
            }
        });
    }

    public static void main(String[] args) {
        launch(args);
    }
}
